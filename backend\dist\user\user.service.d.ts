import { User } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { MinioService, FileInfo } from "src/minio/minio.service";
import { UserNameGeneratorService } from "./user-name-generator/user-name-generator.service";
export type CreateUser = {
    referrerId: string | null;
    email: string;
};
export declare class UserService {
    private readonly prisma;
    private readonly minioService;
    private readonly userNameGeneratorService;
    constructor(prisma: PrismaService, minioService: MinioService, userNameGeneratorService: UserNameGeneratorService);
    getUsers(input: User.GetUsersInput, currentUser: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        image: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        } | null;
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        alignmentSystemType: import("@prisma/client").$Enums.UserAlignmentSystemType | null;
    }[]>;
    getUserByEmail(email: string): Promise<{
        id: string;
        imageId: string | null;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        alignmentSystemType: import("@prisma/client").$Enums.UserAlignmentSystemType | null;
    } | null>;
    getUser(id: string, currentUser: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        image: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        } | null;
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        alignmentSystemType: import("@prisma/client").$Enums.UserAlignmentSystemType | null;
    } | undefined>;
    createUser(data: CreateUser): Promise<{
        id: string;
        imageId: string | null;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        alignmentSystemType: import("@prisma/client").$Enums.UserAlignmentSystemType | null;
    }>;
    updateUser(input: User.UpdateUserInput, currentUser: CurrentUser): Promise<{
        id: string;
        imageId: string | null;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        referrerId: string | null;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        alignmentSystemType: import("@prisma/client").$Enums.UserAlignmentSystemType | null;
    }>;
    updateUserImage(userId: string, file: FileInfo, currentUser: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        url: string;
    }>;
}
